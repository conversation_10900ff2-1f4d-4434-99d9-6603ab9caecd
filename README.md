# SoEasyPack

这是一个简单的 GUI 应用程序，用于精简文件和目录。

## 设置

1.  **克隆仓库 (如果尚未克隆):**

    ```bash
    git clone <仓库地址>
    cd SoEasyPack
    ```

2.  **安装依赖:**

    建议使用 Python 3.x。请确保您的系统上已安装 Python。

    ```bash
    py -m pip install -r requirements.txt
    ```

## 运行应用程序

要启动精简 GUI，请运行:

```bash
py -m soeasypack.gui.slimming_gui
```

## 功能

-   **输入目录选择:** 选择您要精简的源目录。
-   **输出目录选择:** 选择精简后文件将保存的目标目录。
-   **开始精简:** 开始精简过程。

## 注意

目前的精简功能是一个占位符，它会将输入目录中的所有文件和文件夹复制到输出目录。未来的版本可能会添加更复杂的精简逻辑。 