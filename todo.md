用户明确表示“不修改，只出报告”。这意味着我不能进行任何代码修改，只能提供一个详细的计划报告。

我需要根据之前对文件的分析，整理出一份详细的报告，说明如何实现用户提出的两个需求：
1.  将普通模式生成后的自动精简过程和功能分离，普通打包模式不自动精简。
2.  精简的工作可以额外指定文件夹只精简文件夹内的部分。

报告将包含：
*   相关文档洞察（如果存在，但目前没有发现专门的文档文件，所以这部分会简略）。
*   具体的类型定义及其精确位置。
*   相关的实现，包括文件路径、涉及的函数或方法，以及它们如何工作的简要说明。
*   关键依赖及其与查询相关的角色。

我将按照这个结构来组织我的报告。
好的，我明白了。我将根据您的要求，提供一份详细的报告，说明如何实现您提出的功能，而不进行任何代码修改。

---

### **项目精简功能分离与增强计划报告**

**查询目标：**
1.  将普通打包模式（`pack_mode=1`）生成后的自动精简过程和功能分离，使普通打包模式不再自动精简。
2.  精简功能可以额外指定文件夹，只精简文件夹内的部分内容。

---

#### **1. 相关文档洞察**

当前项目结构中未发现专门的“docs”文件夹或详细的架构文档。对功能的理解主要基于对源代码的直接分析。

---

#### **2. 具体类型定义及其精确位置**

*   **`KwargsType`**：在 [`soeasypack/core/easy_pack.py:27`](soeasypack/core/easy_pack.py:27) 定义，用于 `to_pack` 函数的 `**kwargs` 参数，包含 `file_version`、`product_name` 和 `company` 等可选字符串类型。
*   **`pack_mode` 类型定义**：在 [`soeasypack/core/easy_pack.py:481`](soeasypack/core/easy_pack.py:481) 的 `to_pack` 函数签名中，`pack_mode` 参数被定义为 `Literal[0, 1, 2, 3]`，表示其只能是 0、1、2 或 3 中的一个整数值。

---

#### **3. 相关实现与功能分析**

**3.1. 普通模式自动精简功能的分离**

*   **当前实现：**
    *   **主界面 (`main_gui.py`)**：
        *   [`soeasypack/gui/main_gui.py:66`](soeasypack/gui/main_gui.py:66) 定义了 `pack_mode_combo` 下拉框，其中包含“1/普通模式”。
        *   [`soeasypack/gui/main_gui.py:113`](soeasypack/gui/main_gui.py:113) 的 `start_pack` 方法获取 `pack_mode` 的索引值（0, 1, 2, 3），并将其传递给 `to_pack` 函数。
    *   **打包核心 (`easy_pack.py`)**：
        *   [`soeasypack/core/easy_pack.py:481`](soeasypack/core/easy_pack.py:481) 定义了 `to_pack` 函数，接收 `pack_mode` 参数。
        *   [`soeasypack/core/easy_pack.py:561`](soeasypack/core/easy_pack.py:561) 处，当 `pack_mode == 1` 时，`to_pack` 函数会显式调用 `to_slim_file` 函数：
            ```python
            561 |     if pack_mode == 1:
            562 |         to_slim_file(new_main_py_path, check_dir=rundep_dir, project_dir=save_dir, monitoring_time=monitoring_time,
            563 |                      pack_mode=pack_mode, delay_time=delay_time)
            ```
    *   **精简核心 (`slimfile.py`)**：
        *   [`soeasypack/core/slimfile.py:239`](soeasypack/core/slimfile.py:239) 定义了 `to_slim_file` 函数，负责执行实际的精简逻辑。它内部调用 `check_dependency_files` (用于识别依赖) 和 `move_files` (用于移除非依赖文件)。

*   **分离计划：**
    要使普通打包模式不自动精简，需要修改 [`soeasypack/core/easy_pack.py`](soeasypack/core/easy_pack.py)。
    *   **建议修改位置：** [`soeasypack/core/easy_pack.py:561`](soeasypack/core/easy_pack.py:561)
    *   **修改方案：** 将 `if pack_mode == 1:` 条件下的 `to_slim_file` 调用移除或注释掉。
        ```python
        # 移除或注释掉以下代码块
        # if pack_mode == 1:
        #     to_slim_file(new_main_py_path, check_dir=rundep_dir, project_dir=save_dir, monitoring_time=monitoring_time,
        #                  pack_mode=pack_mode, delay_time=delay_time)
        ```
    *   **影响：** 这样修改后，当用户在主界面选择“普通模式”进行打包时，将不再触发自动精简过程。精简功能将完全由独立的“精简界面”控制。

**3.2. 精简工作额外指定文件夹只精简文件夹内的部分**

*   **当前实现：**
    *   **精简界面 (`slimming_gui.py`)**：
        *   [`soeasypack/gui/slimming_gui.py:18`](soeasypack/gui/slimming_gui.py:18) 和 [`soeasypack/gui/slimming_gui.py:28`](soeasypack/gui/slimming_gui.py:28) 提供了输入目录和输出目录的选择。
        *   [`soeasypack/gui/slimming_gui.py:47`](soeasypack/gui/slimming_gui.py:47) 有一个 `slim_all_checkbox`，当前逻辑是精简整个输入目录。
        *   [`soeasypack/gui/slimming_gui.py:74`](soeasypack/gui/slimming_gui.py:74) 的 `start_slimming` 方法调用了占位符精简逻辑，目前只是简单复制文件。
    *   **精简核心 (`slimfile.py`)**：
        *   [`soeasypack/core/slimfile.py:239`](soeasypack/core/slimfile.py:239) 的 `to_slim_file` 函数接收 `check_dir` (需要瘦身的目录) 和 `project_dir` (项目目录)。
        *   [`soeasypack/core/slimfile.py:196`](soeasypack/core/slimfile.py:196) 的 `move_files` 函数遍历 `check_dir` 下的所有文件，并根据 `dependency_files` 列表决定是否移动文件。它没有直接支持按子文件夹或文件模式进行选择性精简的参数。

*   **增强计划：**
    要实现指定文件夹精简，需要修改 [`soeasypack/gui/slimming_gui.py`](soeasypack/gui/slimming_gui.py) 和 [`soeasypack/core/slimfile.py`](soeasypack/core/slimfile.py)。

    *   **建议修改 `slimming_gui.py`：**
        *   **界面添加：** 在 `SlimmingGUI.initUI` 方法中，在“选项”组下添加一个新的输入控件（例如 `QLineEdit` 或 `QTextEdit`），用于用户输入要精简的特定子目录或文件模式（例如，`"dir1/*.py, dir2/, file.txt"`）。
            *   **示例代码（概念性）：**
                ```python
                # 在 options_layout.addWidget(self.slim_all_checkbox) 之后
                self.specific_paths_edit = QLineEdit()
                self.specific_paths_edit.setPlaceholderText("指定要精简的子目录或文件模式 (例如: dir1/*.py, dir2/)")
                options_layout.addWidget(self.specific_paths_edit)
                ```
        *   **逻辑修改：** 在 `SlimmingGUI.start_slimming` 方法中，获取 `specific_paths_edit` 的内容。当调用 `to_slim_file` 时，将此内容作为新参数传递。
            *   **示例代码（概念性）：**
                ```python
                # 在 start_slimming 方法中
                specific_paths = self.specific_paths_edit.text().strip()
                # ...
                # 调用 to_slim_file，假设它现在接受 specific_paths 参数
                from soeasypack.core.slimfile import to_slim_file
                to_slim_file(main_run_path=None, check_dir=input_dir, project_dir=output_dir,
                             specific_paths=specific_paths if not self.slim_all_checkbox.isChecked() else None)
                ```

    *   **建议修改 `slimfile.py`：**
        *   **函数签名修改：** 修改 `to_slim_file` 函数的签名，添加一个 `specific_paths: str = None` 参数。
            *   **示例代码（概念性）：**
                ```python
                # 239 | def to_slim_file(main_run_path: str, check_dir: str, project_dir: str = None,
                # 240 |                  monitoring_time: int = 18, pack_mode=1, delay_time: int = 3,
                # 241 |                  specific_paths: str = None) -> None: # 添加此参数
                ```
        *   **精简逻辑修改：** `move_files` 函数需要增强，以根据 `specific_paths` 参数进行过滤。
            *   **方案一 (在 `move_files` 内部过滤)：** 在遍历 `check_dir` 时，对于每个文件，除了检查它是否在 `dependency_files` 中，还需要检查它是否符合 `specific_paths` 中指定的模式或路径。这可能涉及：
                *   解析 `specific_paths` 字符串，将其分解为多个路径或模式。
                *   使用 `os.path.commonpath` 或 `fnmatch.fnmatch` 来判断当前文件是否属于用户指定的精简范围。
                *   只有当文件既不在 `dependency_files` 中，又符合 `specific_paths` 的条件时，才进行移动。
            *   **方案二 (在 `to_slim_file` 中预处理 `dependency_files`)：** 在调用 `move_files` 之前，根据 `specific_paths` 对 `dependency_files` 进行预处理，只保留那些用户明确指定要精简的路径下的依赖文件。这种方法可能更复杂，因为 `dependency_files` 是要保留的文件，而不是要移除的文件。

            **推荐方案：** 方案一更直接，在 `move_files` 内部对要移除的文件进行二次过滤。

            *   **示例代码（概念性，`move_files` 内部逻辑）：**
                ```python
                # 196 | def move_files(check_dir, project_dir, dependency_files, specific_paths=None):
                # ...
                # 在 for filename in files: 循环内部
                # ...
                # if src_file in dependency_files:
                #     continue
                # else:
                #     # 新增逻辑：如果指定了 specific_paths，则检查文件是否符合条件
                #     if specific_paths:
                #         # 解析 specific_paths，例如按逗号分隔，并处理通配符
                #         # 检查 src_file 是否与 specific_paths 中的任何一个模式匹配
                #         # 如果不匹配，则跳过此文件
                #         is_target_for_slimming = False
                #         for pattern in specific_paths.split(','): # 假设逗号分隔
                #             pattern = pattern.strip()
                #             if pattern.endswith('/'): # 如果是目录，检查是否是子路径
                #                 if src_file.startswith(os.path.join(check_dir, pattern).replace('\\', '/')):
                #                     is_target_for_slimming = True
                #                     break
                #             elif fnmatch.fnmatch(os.path.basename(src_file), pattern): # 检查文件名是否匹配模式
                #                 if os.path.dirname(src_file).startswith(os.path.join(check_dir, os.path.dirname(pattern)).replace('\\', '/')):
                #                     is_target_for_slimming = True
                #                     break
                #             elif src_file == os.path.join(check_dir, pattern).replace('\\', '/'): # 精确文件匹配
                #                 is_target_for_slimming = True
                #                 break
                #         if not is_target_for_slimming:
                #             continue # 不在指定精简范围内，跳过

                #     try:
                #         # ... 现有移动文件的逻辑
                ```
    *   **影响：** 这将允许用户通过精简界面精确控制哪些子目录或文件应该被精简，而不是简单地精简整个 `rundep` 目录。

---

#### **4. 关键依赖及其角色**

*   **`soeasypack.core.easy_pack`**：主打包逻辑，负责协调整个打包流程，包括环境复制、脚本复制、编译和构建 EXE。
*   **`soeasypack.gui.main_gui`**：提供主用户界面，收集打包参数，并触发 `easy_pack.to_pack`。
*   **`soeasypack.gui.slimming_gui`**：提供独立的精简用户界面，收集精简参数，并触发 `slimfile.to_slim_file`。
*   **`soeasypack.core.slimfile`**：核心精简逻辑，依赖 `Procmon64.exe` (外部工具) 进行文件访问监控以识别依赖，并执行文件的移动操作。
*   **`soeasypack.core.my_logger`**：用于日志输出，方便用户了解程序运行状态。
*   **`PySide6`**：用于构建图形用户界面。
*   **`subprocess`**：用于执行外部命令，如 `Procmon64.exe` 和 Go 编译器。
*   **`shutil`, `os`, `pathlib`**：用于文件和目录操作。
*   **`csv`**：用于读写依赖文件列表。
*   **`threading`, `concurrent.futures`**：用于实现并行文件复制，提高效率。

---

**总结：**

通过上述计划，可以实现普通打包模式与精简功能的解耦，并为精简功能提供更细粒度的控制。核心修改点在于 `easy_pack.py` 中移除普通模式下的自动精简调用，以及在 `slimming_gui.py` 和 `slimfile.py` 中引入并处理 `specific_paths` 参数，以实现指定文件夹的精简。

---