import sys
import logging
import sys
import threading
from PySide6.QtWidgets import <PERSON>A<PERSON><PERSON>, QWidget, QVBoxLayout, QPushButton, QLabel, QLineEdit, QFileDialog, QCheckBox, QComboBox, QGroupBox, QHBoxLayout, QTextEdit, QMainWindow
from PySide6.QtCore import Signal, QObject

from soeasypack.core.easy_pack import to_pack
from soeasypack.gui.slimming_gui import SlimmingGUI

my_logger = logging.getLogger(__name__)

class SoEasyPackGUI(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("SoEasyPack 图形界面")
        self.setGeometry(100, 100, 800, 600)
        self.initUI()

    def initUI(self):
        main_layout = QVBoxLayout()

        # 输入文件和输出目录选择
        input_group = QGroupBox("文件和目录设置")
        input_layout = QVBoxLayout()

        self.main_py_path_edit = QLineEdit()
        self.main_py_path_edit.setPlaceholderText("主入口文件路径")
        main_py_browse_btn = QPushButton("浏览...")
        main_py_browse_btn.clicked.connect(self.browse_main_py_path)

        self.save_dir_edit = QLineEdit()
        self.save_dir_edit.setPlaceholderText("打包保存目录")
        save_dir_browse_btn = QPushButton("浏览...")
        save_dir_browse_btn.clicked.connect(self.browse_save_dir)

        h_layout1 = QHBoxLayout()
        h_layout1.addWidget(QLabel("主入口文件:"))
        h_layout1.addWidget(self.main_py_path_edit)
        h_layout1.addWidget(main_py_browse_btn)

        h_layout2 = QHBoxLayout()
        h_layout2.addWidget(QLabel("保存目录:"))
        h_layout2.addWidget(self.save_dir_edit)
        h_layout2.addWidget(save_dir_browse_btn)

        input_layout.addLayout(h_layout1)
        input_layout.addLayout(h_layout2)
        input_group.setLayout(input_layout)
        main_layout.addWidget(input_group)

        # 打包选项
        options_group = QGroupBox("打包选项")
        options_layout = QVBoxLayout()

        self.exe_name_edit = QLineEdit("main")
        exe_name_layout = QHBoxLayout()
        exe_name_layout.addWidget(QLabel("可执行文件名称:"))
        exe_name_layout.addWidget(self.exe_name_edit)
        options_layout.addLayout(exe_name_layout)

        self.hide_cmd_checkbox = QCheckBox("隐藏控制台窗口")
        self.hide_cmd_checkbox.setChecked(True)
        options_layout.addWidget(self.hide_cmd_checkbox)

        self.pack_mode_combo = QComboBox()
        self.pack_mode_combo.addItems(["0/快速打包模式", "1/普通模式", "2/轻量模式", "3/AST模式"])
        pack_mode_layout = QHBoxLayout()
        pack_mode_layout.addWidget(QLabel("打包模式:"))
        pack_mode_layout.addWidget(self.pack_mode_combo)
        options_layout.addLayout(pack_mode_layout)

        self.embed_exe_checkbox = QCheckBox("嵌入脚本到EXE")
        options_layout.addWidget(self.embed_exe_checkbox)

        self.onefile_checkbox = QCheckBox("生成单文件EXE")
        options_layout.addWidget(self.onefile_checkbox)

        options_group.setLayout(options_layout)
        main_layout.addWidget(options_group)

        # 日志输出区域
        self.log_output = QTextEdit()
        self.log_output.setReadOnly(True)
        main_layout.addWidget(self.log_output)

        self.setLayout(main_layout)

        # 设置日志处理器
        self.log_handler = QtLogHandler(self.log_output)
        my_logger.addHandler(self.log_handler)
        my_logger.setLevel(logging.INFO)
        # Slimming button
        self.slimming_button = QPushButton("精简")
        self.slimming_button.clicked.connect(self.open_slimming_gui)
        main_layout.addWidget(self.slimming_button)

    def browse_main_py_path(self):
        file_path, _ = QFileDialog.getOpenFileName(self, "选择主入口文件", "", "Python Files (*.py);;All Files (*)")
        if file_path:
            self.main_py_path_edit.setText(file_path)

    def browse_save_dir(self):
        dir_path = QFileDialog.getExistingDirectory(self, "选择打包保存目录")
        if dir_path:
            self.save_dir_edit.setText(dir_path)

    def start_pack(self):
        main_py_path = self.main_py_path_edit.text()
        save_dir = self.save_dir_edit.text()
        exe_name = self.exe_name_edit.text()
        hide_cmd = self.hide_cmd_checkbox.isChecked()
        pack_mode = self.pack_mode_combo.currentIndex() # 0, 1, 2, 3
        embed_exe = self.embed_exe_checkbox.isChecked()
        onefile = self.onefile_checkbox.isChecked()

        if not main_py_path or not save_dir:
            my_logger.error("主入口文件路径和保存目录不能为空！")
            return

        self.log_output.clear()
        my_logger.info("开始打包...")
        # 在单独的线程中运行打包函数，以避免阻塞GUI
        threading.Thread(target=self._run_pack_in_thread, args=(
            main_py_path, save_dir, exe_name, hide_cmd, pack_mode, embed_exe, onefile
        )).start()

    def open_slimming_gui(self):
        self.slimming_gui = SlimmingGUI()
        self.slimming_gui.show()

        def _run_pack_in_thread(self, main_py_path, save_dir, exe_name, hide_cmd, pack_mode, embed_exe, onefile):
            try:
                to_pack(
                    main_py_path=main_py_path,
                    save_dir=save_dir,
                    exe_name=exe_name,
                    hide_cmd=hide_cmd,
                    pack_mode=pack_mode,
                    embed_exe=embed_exe,
                    onefile=onefile
                )
                my_logger.info("打包完成！")
            except Exception as e:
                my_logger.error(f"打包失败: {e}")


class SignalLogger(QObject):
    log_signal = Signal(str)


class QtLogHandler(logging.Handler):
    def __init__(self, text_widget):
        super().__init__()
        self.text_widget = text_widget
        self.signal_logger = SignalLogger()
        self.signal_logger.log_signal.connect(self.append_log)

    def emit(self, record):
        msg = self.format(record)
        self.signal_logger.log_signal.emit(msg)

    def append_log(self, msg):
        self.text_widget.append(msg)


if __name__ == '__main__':
    app = QApplication(sys.argv)
    ex = SoEasyPackGUI()
    ex.show()
    window = QMainWindow()
    central_widget = QWidget()
    central_layout = QVBoxLayout(central_widget)
    central_layout.addWidget(ex)
    window.setCentralWidget(central_widget)
    window.show()

    sys.exit(app.exec())
