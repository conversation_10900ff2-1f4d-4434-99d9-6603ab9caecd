import sys
from PySide6.QtWidgets import QApp<PERSON>, QWidget, QVBoxLayout, QPushButton, QLabel, QLineEdit, QFileDialog, QCheckBox, QGroupBox, QHBoxLayout

class SlimmingGUI(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("精简界面")
        self.setGeometry(100, 100, 600, 400)
        self.initUI()

    def initUI(self):
        main_layout = QVBoxLayout()

        # 输入文件和输出目录选择
        input_group = QGroupBox("目录设置")
        input_layout = QVBoxLayout()

        self.input_dir_edit = QLineEdit()
        self.input_dir_edit.setPlaceholderText("要精简的输入目录 (例如: C:/我的项目)")
        input_dir_browse_btn = QPushButton("浏览...")
        input_dir_browse_btn.clicked.connect(self.browse_input_dir)

        h_layout1 = QHBoxLayout()
        h_layout1.addWidget(QLabel("输入目录:"))
        h_layout1.addWidget(self.input_dir_edit)
        h_layout1.addWidget(input_dir_browse_btn)

        self.output_dir_edit = QLineEdit()
        self.output_dir_edit.setPlaceholderText("精简后的输出目录 (例如: C:/我的精简项目)")
        output_dir_browse_btn = QPushButton("浏览...")
        output_dir_browse_btn.clicked.connect(self.browse_output_dir)

        h_layout2 = QHBoxLayout()
        h_layout2.addWidget(QLabel("输出目录:"))
        h_layout2.addWidget(self.output_dir_edit)
        h_layout2.addWidget(output_dir_browse_btn)

        input_layout.addLayout(h_layout1)
        input_layout.addLayout(h_layout2)
        input_group.setLayout(input_layout)
        main_layout.addWidget(input_group)

        # 选项
        options_group = QGroupBox("选项")
        options_layout = QVBoxLayout()

        self.slim_all_checkbox = QCheckBox("精简目录中所有文件")
        self.slim_all_checkbox.setChecked(True)
        options_layout.addWidget(self.slim_all_checkbox)

        options_group.setLayout(options_layout)
        main_layout.addWidget(options_group)

        # Slimming button
        self.slim_button = QPushButton("开始精简")
        self.slim_button.clicked.connect(self.start_slimming)
        main_layout.addWidget(self.slim_button)

        self.status_label = QLabel("")
        main_layout.addWidget(self.status_label)

        self.setLayout(main_layout)

    def browse_input_dir(self):
        dir_path = QFileDialog.getExistingDirectory(self, "选择输入目录")
        if dir_path:
            self.input_dir_edit.setText(dir_path)

    def browse_output_dir(self):
        dir_path = QFileDialog.getExistingDirectory(self, "选择输出目录")
        if dir_path:
            self.output_dir_edit.setText(dir_path)

    def start_slimming(self):
        input_dir = self.input_dir_edit.text()
        output_dir = self.output_dir_edit.text()

        if not input_dir:
            self.status_label.setText("错误: 请选择输入目录。")
            return
        if not output_dir:
            self.status_label.setText("错误: 请选择输出目录。")
            return

        self.status_label.setText(f"正在从 '{input_dir}' 精简到 '{output_dir}'...")
        # 实际的精简逻辑将在这里实现
        # 目前只是一个占位符
        try:
            import os
            import shutil

            # 确保输出目录存在
            if not os.path.exists(output_dir):
                os.makedirs(output_dir)

            for item in os.listdir(input_dir):
                s = os.path.join(input_dir, item)
                d = os.path.join(output_dir, item)
                if os.path.isdir(s):
                    shutil.copytree(s, d, dirs_exist_ok=True)
                else:
                    shutil.copy2(s, d)
            self.status_label.setText("精简完成！")
        except Exception as e:
            self.status_label.setText(f"精简失败: {e}")

if __name__ == '__main__':
    app = QApplication(sys.argv)
    ex = SlimmingGUI()
    ex.show()
    sys.exit(app.exec_())
